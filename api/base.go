package api

import (
	"github.com/gin-gonic/gin"

	"gate.io/cmp/nacos-config/pkg/constant"
)

type Response struct {
	RequestId string      `json:"request_id"`
	Code      int         `json:"code"`
	Message   string      `json:"msg"`
	Data      interface{} `json:"data"`
}

// JSON 发送json格式的数据
func handler(ctx *gin.Context, code constant.ResponseCode, err error, data interface{}) {
	message := "成功"
	if err != nil {
		message = err.Error()
	}

	ctx.JSON(200, Response{
		RequestId: ctx.GetString(constant.RequestId),
		Code:      int(code),
		Message:   message,
		Data:      data,
	})
}

func HandleSuccess(ctx *gin.Context, data interface{}) {
	handler(ctx, constant.Success, nil, data)
}

func HandleError(ctx *gin.Context, code constant.ResponseCode, err error, data interface{}) {
	handler(ctx, code, err, data)
}

// func GetUserInfoFromCtx(ctx context.Context) *AccountInfo {
// 	v := ctx.Value(constant.UserInfoKey)
// 	if v != nil {
// 		return v.(*AccountInfo)
// 	}

// 	return nil
// }
