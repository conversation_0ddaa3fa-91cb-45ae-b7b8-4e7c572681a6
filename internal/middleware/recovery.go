package middleware

import (
	"fmt"
	"runtime/debug"

	"github.com/gin-gonic/gin"

	"gate.io/cmp/nacos-config/api"
	"gate.io/cmp/nacos-config/pkg/constant"
	"gate.io/cmp/nacos-config/pkg/log"
)

func ServerRecovery() gin.HandlerFunc {
	return func(ctx *gin.Context) {

		defer func() {
			if err := recover(); err != nil {
				log.RID(ctx).Error("was panic.",
					log.Pair("error", err),
					log.Pair("stack", string(debug.Stack())),
				)
				api.HandleError(ctx, constant.FataError, fmt.<PERSON>("系统异常, 请联系管理员"), nil)
				ctx.Abort()
				return
			}
		}()
		ctx.Next()
	}
}
