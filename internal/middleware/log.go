package middleware

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"io"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"gate.io/cmp/nacos-config/pkg/constant"
	"gate.io/cmp/nacos-config/pkg/log"
)

// RequestId 用来设置和透传requestId
func RequestId() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		traceId := ctx.Request.Header.Get(constant.TraceIdHeader)
		if traceId == "" {
			hash := md5.Sum([]byte(uuid.NewString()))
			traceId = hex.EncodeToString(hash[:])
		}
		ctx.Header("X-Trace-ID", traceId)

		ctx.Set(constant.RequestId, traceId)
		ctx.Request.WithContext(context.WithValue(ctx.Request.Context(), constant.RequestId, traceId))
		ctx.Next()
	}
}

// Logger 记录每次请求的请求信息和响应信息
func Logger() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// 请求前
		t := time.Now()
		reqPath := ctx.Request.URL.Path
		reqId := ctx.GetString(constant.RequestId)
		method := ctx.Request.Method
		ip := ctx.ClientIP()

		var requestBody []byte
		if ctx.Request.Header.Get("Content-Type") == "application/json" {
			requestBody, err := io.ReadAll(ctx.Request.Body)
			if err != nil {
				requestBody = []byte{}
			}
			ctx.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// log.Info(fmt.Sprintf("%s %s start", method, reqPath),
		// 	log.Pair(constant.RequestId, reqId),
		// 	log.Pair("host", ip),
		// 	log.Pair("path", reqPath),
		// 	log.Pair("method", method),
		// 	log.Pair("body", string(requestBody)))

		ctx.Next()
		// 请求后
		latency := time.Since(t)
		log.Info(fmt.Sprintf("request %s", reqPath),
			log.Pair(constant.RequestId, reqId),
			log.Pair("method", method),
			log.Pair("host", ip),
			log.Pair("body", string(requestBody)),
			log.Pair("path", reqPath),
			log.Pair("cost", latency))
	}
}
