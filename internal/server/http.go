package server

import (
	"github.com/gin-gonic/gin"

	"gate.io/cmp/nacos-config/api"
	"gate.io/cmp/nacos-config/internal/middleware"
	"gate.io/cmp/nacos-config/pkg/config"
	"gate.io/cmp/nacos-config/pkg/server/http"
	"gate.io/cmp/nacos-config/pkg/validator"
)

func NewHTTPServer() *http.Server {

	gin.SetMode(config.GlobalConfig.Mode)
	s := http.NewServer(
		gin.Default(),
		http.WithServerPort(config.GlobalConfig.Port),
	)

	// gin validator替换
	validator.LazyInitGinValidator("zh")

	s.GET("/health", func(ctx *gin.Context) {
		api.HandleSuccess(ctx, map[string]any{
			"msg": "ok",
		})
	})

	v1 := s.Group("/v1")
	v1.Use(
		middleware.RequestId(),
		middleware.Logger(),
		middleware.ServerRecovery(),
	)

	return s
}
