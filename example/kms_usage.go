package main

import (
	"context"
	"fmt"
	"log"

	"gate.io/cmp/nacos-config/pkg/aws"
	"gate.io/cmp/nacos-config/pkg/config"
)

func main() {
	// 加载配置文件
	cfg := config.Load("config/local.yml")
	
	// 创建 KMS 客户端
	kmsClient, err := aws.NewAWSKMS(&cfg.AWSConfig)
	if err != nil {
		log.Fatalf("Failed to create KMS client: %v", err)
	}

	ctx := context.Background()

	// 1. 获取所有以 nacos- 开头的 KMS key
	fmt.Println("=== 获取 nacos- 开头的 KMS keys ===")
	nacosKeys, err := kmsClient.ListNacosKeys(ctx)
	if err != nil {
		log.Fatalf("Failed to list nacos keys: %v", err)
	}

	fmt.Printf("Found %d nacos keys:\n", len(nacosKeys))
	for i, key := range nacosKeys {
		fmt.Printf("%d. Key ID: %s\n", i+1, *key.KeyId)
	}

	// 2. 如果有可用的 key，进行加密操作
	if len(nacosKeys) > 0 {
		keyID := *nacosKeys[0].KeyId
		plaintext := "Hello, this is a test message for nacos configuration!"

		fmt.Printf("\n=== 使用 key %s 进行加密 ===\n", keyID)
		fmt.Printf("原文: %s\n", plaintext)

		// 加密
		ciphertext, err := kmsClient.Encrypt(ctx, plaintext, keyID)
		if err != nil {
			log.Fatalf("Failed to encrypt: %v", err)
		}

		fmt.Printf("加密后的数据长度: %d bytes\n", len(ciphertext))

		// 解密（验证）
		decrypted, err := kmsClient.Decrypt(ctx, ciphertext)
		if err != nil {
			log.Fatalf("Failed to decrypt: %v", err)
		}

		fmt.Printf("解密后: %s\n", decrypted)

		if plaintext == decrypted {
			fmt.Println("✅ 加密解密测试成功！")
		} else {
			fmt.Println("❌ 加密解密测试失败！")
		}
	} else {
		fmt.Println("⚠️  没有找到以 nacos- 开头的 KMS key")
		fmt.Println("请在 AWS KMS 中创建一个别名以 'nacos-' 开头的 key")
	}
}
