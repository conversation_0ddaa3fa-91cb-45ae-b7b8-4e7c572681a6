package aws

import (
	"context"
	"fmt"
	"strings"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/kms"
	"github.com/aws/aws-sdk-go-v2/service/kms/types"

	appConfig "gate.io/cmp/nacos-config/pkg/config"
)

// AWSKMS wraps AWS KMS client with custom methods
type AWSKMS struct {
	client *kms.Client
	config *appConfig.AWSConfig
}

// NewAWSKMS creates a new AWSKMS instance with configuration
func NewAWSKMS(awsConfig *appConfig.AWSConfig) (*AWSKMS, error) {
	if awsConfig == nil {
		return nil, fmt.Errorf("AWS config cannot be nil")
	}

	if awsConfig.AccessKey == "" || awsConfig.SecretKey == "" || awsConfig.Region == "" {
		return nil, fmt.Errorf("AWS AccessKey, SecretKey and Region are required")
	}

	// Create AWS config with credentials
	cfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithRegion(awsConfig.Region),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(
			awsConfig.AccessKey,
			awsConfig.SecretKey,
			"", // session token (optional)
		)),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to load AWS config: %w", err)
	}

	// Create KMS client
	client := kms.NewFromConfig(cfg)

	return &AWSKMS{
		client: client,
		config: awsConfig,
	}, nil
}

// ListNacosKeys returns all KMS keys that start with "nacos-"
func (k *AWSKMS) ListNacosKeys(ctx context.Context) ([]types.KeyListEntry, error) {
	var nacosKeys []types.KeyListEntry
	var nextMarker *string

	for {
		input := &kms.ListKeysInput{
			Limit:  aws.Int32(100), // AWS KMS allows max 1000, but we use 100 for better performance
			Marker: nextMarker,
		}

		result, err := k.client.ListKeys(ctx, input)
		if err != nil {
			return nil, fmt.Errorf("failed to list KMS keys: %w", err)
		}

		// Filter keys that start with "nacos-"
		for _, key := range result.Keys {
			// Get key description to check the alias
			describeInput := &kms.DescribeKeyInput{
				KeyId: key.KeyId,
			}

			_, err := k.client.DescribeKey(ctx, describeInput)
			if err != nil {
				// Skip this key if we can't describe it
				continue
			}

			// Check if key has aliases that start with "nacos-"
			aliasInput := &kms.ListAliasesInput{
				KeyId: key.KeyId,
			}

			aliasResult, err := k.client.ListAliases(ctx, aliasInput)
			if err != nil {
				// Skip this key if we can't list aliases
				continue
			}

			// Check if any alias starts with "nacos-"
			for _, alias := range aliasResult.Aliases {
				if alias.AliasName != nil && strings.HasPrefix(*alias.AliasName, "alias/nacos-") {
					nacosKeys = append(nacosKeys, key)
					break // Found one matching alias, no need to check others
				}
			}
		}

		// Check if there are more keys to fetch
		if !result.Truncated {
			break
		}
		nextMarker = result.NextMarker
	}

	return nacosKeys, nil
}

// Encrypt encrypts a string using the specified KMS key
func (k *AWSKMS) Encrypt(ctx context.Context, plaintext string, keyID string) ([]byte, error) {
	if plaintext == "" {
		return nil, fmt.Errorf("plaintext cannot be empty")
	}

	if keyID == "" {
		return nil, fmt.Errorf("keyID cannot be empty")
	}

	input := &kms.EncryptInput{
		KeyId:     aws.String(keyID),
		Plaintext: []byte(plaintext),
	}

	result, err := k.client.Encrypt(ctx, input)
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt data with key %s: %w", keyID, err)
	}

	return result.CiphertextBlob, nil
}

// Decrypt decrypts data using KMS (bonus method for completeness)
func (k *AWSKMS) Decrypt(ctx context.Context, ciphertext []byte) (string, error) {
	if len(ciphertext) == 0 {
		return "", fmt.Errorf("ciphertext cannot be empty")
	}

	input := &kms.DecryptInput{
		CiphertextBlob: ciphertext,
	}

	result, err := k.client.Decrypt(ctx, input)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt data: %w", err)
	}

	return string(result.Plaintext), nil
}
