package constant

const (
	// RequestId 请求id名称
	RequestId     = "request_id"
	TraceIdHeader = "X-Trace-ID"

	// TimeLayout 时间格式
	TimeLayout   = "2006-01-02 15:04:05"
	TimeLayoutMs = "2006-01-02 15:04:05.000"
	TimeLayoutT  = "2006-01-02T15:04:05"
)

type ResponseCode int

const (
	//成功
	Success ResponseCode = 200
	// 参数错误
	ErrorParams ResponseCode = 400
	// 未登录
	Unauthorized ResponseCode = 401
	// # 菜单或权限拒绝
	Forbiden ResponseCode = 403
	// 记录不存在
	NotFound ResponseCode = 404
	// 程序内部异常
	FataError ResponseCode = 500
)
