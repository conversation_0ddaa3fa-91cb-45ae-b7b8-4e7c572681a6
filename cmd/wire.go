//go:build wireinject
// +build wireinject

package main

import (
	"context"

	"github.com/google/wire"

	"gate.io/cmp/nacos-config/internal/server"

	"gate.io/cmp/nacos-config/pkg/app"
	"gate.io/cmp/nacos-config/pkg/config"
	"gate.io/cmp/nacos-config/pkg/server/http"
)

// var repositorySet = wire.NewSet(
// )

var serviceSet = wire.NewSet()

var handlerSet = wire.NewSet()

var serverSet = wire.NewSet(
	server.NewHTTPServer,
)

// build App
func newApp(httpServer *http.Server) *app.App {
	return app.NewApp(
		app.WithServer(httpServer),
		app.WithName(config.GlobalConfig.AppName),
	)
}

func NewHttpService(ctx context.Context) (*app.App, func(), error) {
	panic(wire.Build(
		serviceSet,
		handlerSet,
		serverSet,
		newApp,
	))
}
