package main

import (
	"context"
	"flag"

	"gate.io/cmp/nacos-config/pkg/config"
	"gate.io/cmp/nacos-config/pkg/log"
	"gate.io/cmp/nacos-config/pkg/version"
)

type AppOptions struct {
	PrintVersion   bool   // 打印版本
	ConfigFilePath string // 配置文件路径
}

// ResolveAppOptions 解析启动参数
func ResolveAppOptions(opt *AppOptions) {
	var printVersion bool
	var configFilePath string
	flag.BoolVar(&printVersion,
		"v",
		false,
		"-v 选项用于控制是否打印当前项目的版本",
	)
	flag.StringVar(&configFilePath,
		"c", "conf/config-local.yml",
		"-c 选项用于指定要使用的配置文件")
	flag.Parse()

	opt.PrintVersion = printVersion
	opt.ConfigFilePath = configFilePath
}

func main() {
	// 解析服务器启动参数
	appOpt := &AppOptions{}
	ResolveAppOptions(appOpt)
	if appOpt.PrintVersion {
		version.PrintVersion()
	}

	ctx := context.Background()
	// 加载配置文件
	c := config.Load(appOpt.ConfigFilePath)

	log.InitLogger(&c.LogConfig, c.AppName) // 日志
	defer log.Sync()

	app, cleanup, err := NewHttpService(ctx)
	defer cleanup()
	if err != nil {
		panic(err)
	}

	log.Info("server start", log.Pair("port", config.GlobalConfig.Port))

	if err = app.Run(ctx); err != nil {
		panic(err)
	}
}
